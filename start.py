#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 聊天系统启动脚本
自动检查配置并启动聊天系统
"""

import sys
import subprocess

def check_and_start():
    """检查配置并启动系统"""
    print("🔍 正在检查系统配置...")
    
    try:
        # 运行配置测试
        result = subprocess.run([sys.executable, "test_config.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n🚀 配置检查通过，启动聊天系统...")
            # 启动主程序
            subprocess.run([sys.executable, "interactive_chat_system.py"])
        else:
            print("\n❌ 配置检查失败，请先解决配置问题")
            print("💡 运行 python test_config.py 查看详细错误信息")
            
    except FileNotFoundError:
        print("❌ 找不到测试脚本，请确保test_config.py存在")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    check_and_start()
