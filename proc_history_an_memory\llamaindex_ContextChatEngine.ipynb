# =============================
#  共享的 ChatSummaryMemoryBuffer
# =============================
print("\n" + "🌟 创建共享 ChatSummaryMemoryBuffer (token_limit=1000) ...\n")

memory = ChatSummaryMemoryBuffer.from_defaults(
    token_limit = 1000, # 测试用1000，真实用4000，感觉根本用不完
    llm=Settings.llm,  # 用同一个 LLM 进行摘要
    chat_store=chat_store,
    chat_store_key="redis_llama_shortchat_001",
)

print(memory.summarize_prompt)

!pip install -U llama-index qdrant-client llama-index-vector-stores-qdrant llama-index-llms-openai llama-index-embeddings-openai

import os
import llama_index.core
llama_index.core.set_global_handler("simple")  # 打印所有 LLM 的输入/输出
from llama_index.core import Settings
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

# 替换成你自己的 OpenAI API Key
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"



# ➊ 配置全局设置（替代 ServiceContext）
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)


from llama_index.core import Document, VectorStoreIndex
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient


# 准备知识库文档
documents = [
    Document(
        text="FlexiBot的第一个核心功能是多语言翻译。它可以实时翻译超过50种语言，帮助用户跨越语言障碍进行交流。",
        metadata={"doc_name": "功能介绍", "feature_id": "translation"}
    ),
    Document(
        text="FlexiBot的第二个能力是代码生成。它精通Python、JavaScript和SQL，可以根据用户的自然语言描述生成相应的代码片段。",
        metadata={"doc_name": "功能介绍", "feature_id": "coding"}
    ),
    Document(
        text="FlexiBot的第三个亮点是创意写作。无论是市场营销文案、诗歌还是短篇故事，它都能提供富有创意的文本内容。",
        metadata={"doc_name": "功能介绍", "feature_id": "writing"}
    )
]

# 初始化一个内存中的 Qdrant 客户端
client = QdrantClient(location=":memory:")

# 创建一个 QdrantVectorStore 实例
vector_store = QdrantVectorStore(client=client, collection_name="flexibot_docs")

# 从文档创建索引，数据将自动存入 Qdrant
index = VectorStoreIndex.from_documents(
    documents,
    vector_store=vector_store,
)

print("知识库已成功索引到 Qdrant (内存模式)！")

from llama_index.core.memory import ChatMemoryBuffer

# 创建一个内存来存储对话历史
memory = ChatMemoryBuffer.from_defaults(token_limit=2000)

# 从索引创建上下文聊天引擎
context_chat_engine = index.as_chat_engine(
    chat_mode="context",
    memory=memory,
    system_prompt="你是一个名叫 FlexiBot 的乐于助人的AI助手。请根据上下文回答问题。",
    verbose=True
)

# 开始第一轮对话
print("--- 开始对话 (ContextChatEngine) ---\n")
user_message_1 = "FlexiBot有什么功能？"

print('-'*20,"用户message start",'-'*20)
print(f"👤 用户: {user_message_1}")
print('-'*20,"用户message end",'-'*20, "\n\n")

response_1 = context_chat_engine.chat(user_message_1)

print('-'*20,"AI response start",'-'*20)
print(f"🤖 FlexiBot: {response_1}\n")
print('-'*20,"AI response end",'-'*20, "\n\n")

# 第二轮对话，提出一个依赖上一轮对话的内容
user_message_2 = "详细介绍一下第二个功能。"

print('-'*20,"用户message start",'-'*20)
print(f"👤 用户: {user_message_2}")
print('-'*20,"用户message end",'-'*20, "\n\n")

response_2 = context_chat_engine.chat(user_message_2)

print('-'*20,"AI response start",'-'*20)
print(f"🤖 FlexiBot: {response_2}\n")
print('-'*20,"AI response end",'-'*20, "\n\n")

print("--- 对话结束 ---")

# 创建知识库
documents = [
    Document(text="苹果是红色的水果，富含维生素C"),
    Document(text="香蕉是黄色的水果，含有钾元素"),
    Document(text="橙子是橙色的水果，酸甜可口")
]

index = VectorStoreIndex.from_documents(documents)

# 创建自定义聊天引擎，能打印传给LLM的内容
class VerboseContextChatEngine:
    def __init__(self, index):
        self.index = index
        self.memory = ChatMemoryBuffer.from_defaults(token_limit=2000)
        self.retriever = index.as_retriever(similarity_top_k=2)
        
    def chat(self, message):
        # 1. 只用当前消息检索（不用历史）
        print(f"🔍 检索查询: '{message}'")
        retrieved_nodes = self.retriever.retrieve(message)
        
        context = "\n".join([node.text for node in retrieved_nodes])
        print(f"📚 检索到的知识:\n{context}\n")
        
        # 2. 构建完整prompt - 修复这里
        chat_history = ""
        for msg in self.memory.get():
            if hasattr(msg, 'content'):
                role = "用户" if msg.role == "user" else "助手"
                chat_history += f"{role}: {msg.content}\n"
            else:
                # 处理字典格式
                role = "用户" if msg.get("role") == "user" else "助手"
                chat_history += f"{role}: {msg.get('content', '')}\n"
            
        full_prompt = f"""基于以下知识回答问题：

知识库内容：
{context}

对话历史：
{chat_history}

当前问题：{message}

请回答："""
        
        print(f"📝 发送给LLM的完整内容:\n{'-'*50}\n{full_prompt}\n{'-'*50}\n")
        
        # 3. 调用LLM
        response = Settings.llm.complete(full_prompt)
        
        # 4. 更新记忆 - 使用正确的格式
        from llama_index.core.llms import ChatMessage
        self.memory.put(ChatMessage(role="user", content=message))
        self.memory.put(ChatMessage(role="assistant", content=str(response)))
        
        return response

# 测试多轮对话
chat_engine = VerboseContextChatEngine(index)

print("=== 第一轮对话 ===")
response1 = chat_engine.chat("什么水果是红色的？")
print(f"🤖 回答: {response1}\n")

print("=== 第二轮对话 ===")
response2 = chat_engine.chat("它有什么营养价值？")
print(f"🤖 回答: {response2}")

print("重置聊天引擎状态...")
context_chat_engine.reset()
print("状态已重置，可以开始新的对话了。")

from llama_index.core.memory import ChatMemoryBuffer

# 同样，创建一个新的内存实例
memory = ChatMemoryBuffer.from_defaults(token_limit=2000)

# 创建问题压缩聊天引擎
condense_chat_engine = index.as_chat_engine(
    chat_mode="condense_question",
    memory=memory,
    verbose=True  # 开启详细模式，观察内部流程
)

# 开始第一轮对话
print("--- 开始对话 (CondenseQuestionChatEngine) ---\n")
user_message_1 = "FlexiBot有什么功能？"
print(f"👤 用户: {user_message_1}")
response_1 = condense_chat_engine.chat(user_message_1)
print(f"🤖 FlexiBot: {response_1}\n")

# 第二轮对话，同样提出一个依赖上下文的问题
# 注意观察 verbose=True 打印出的 'Condensing query:' 部分
user_message_2 = "详细介绍一下第二个功能。"
print(f"👤 用户: {user_message_2}")
response_2 = condense_chat_engine.chat(user_message_2)
print(f"🤖 FlexiBot: {response_2}\n")

print("--- 对话结束 ---")

# 方法1: 直接查看 CondenseQuestionChatEngine 的属性
condense_chat_engine = index.as_chat_engine(
    chat_mode="condense_question",
    memory=memory,
    verbose=True
)

print("=== CondenseQuestionChatEngine 属性探索 ===")
print("可用属性:", [attr for attr in dir(condense_chat_engine) if not attr.startswith('_')])

# 尝试访问内部组件
if hasattr(condense_chat_engine, '_query_engine'):
    print("\n--- Query Engine 提示词 ---")
    try:
        query_prompts = condense_chat_engine._query_engine.get_prompts()
        for key, prompt in query_prompts.items():
            print(f"{key}: {prompt.get_template()}")
    except:
        print("无法获取 query_engine 提示词")

if hasattr(condense_chat_engine, '_condense_question_prompt'):
    print("\n--- 问题压缩提示词 ---")
    print(condense_chat_engine._condense_question_prompt.get_template())