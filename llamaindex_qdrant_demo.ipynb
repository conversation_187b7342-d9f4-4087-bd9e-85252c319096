!pip install -U llama-index-core llama-index-llms-openai llama-index-vector-stores-qdrant qdrant-client openai

import os
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"

# 测试api_key

import os
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"

# 测试OpenAI连接
from openai import OpenAI

try:
    client = OpenAI()
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": "你好，请回复一句话测试连接"}],
        max_tokens=50
    )
    print("✅ OpenAI连接成功！")
    print(f"回复: {response.choices[0].message.content}")
except Exception as e:
    print("❌ OpenAI连接失败！")
    print(f"错误: {e}")

from llama_index.core import SimpleDirectoryReader

# 把 data/ 里所有文件读成 Document 对象
documents = SimpleDirectoryReader('llama_data').load_data()

# 给每个文档加上示例 metadata
for idx, doc in enumerate(documents, start=1):
    doc.metadata["course_id"] = "course_01"  # 真实场景可根据文件夹层级自动填
    doc.metadata["course_material_id"] = f"material_{idx:03d}"

documents[0]  # 看看结构

from llama_index.core.node_parser import SentenceSplitter

splitter = SentenceSplitter(chunk_size=512, chunk_overlap=50)
nodes = splitter.get_nodes_from_documents(documents)

print(f"总共生成了 {len(nodes)} 个节点")
print("=" * 80)

for i, node in enumerate(nodes):
    print(f"节点 {i+1}:")
    print(node.text)
    print(f"字符数量: {len(node.text)}")
    print("=" * 80)

#把向量写入内存的qdrant

from qdrant_client import QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex, Settings
from llama_index.embeddings.openai import OpenAIEmbedding

# ➊ 配置嵌入模型全局设置（替代 ServiceContext）
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)

# ➋ 连接 Qdrant
qdrant = QdrantClient(":memory:")
vector_store = QdrantVectorStore(collection_name="rw_ai_lms", client=qdrant)
storage_context = StorageContext.from_defaults(vector_store=vector_store)

# ➌ 建索引（不再需要 service_context 参数）
index = VectorStoreIndex(nodes, storage_context=storage_context)

print("✅ 已写入", len(nodes), "条向量")

# 配置llm
from llama_index.llms.openai import OpenAI
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0.2,
    api_base="https://api.openai-proxy.org/v1"
)

# 使用本地qdrant服务的版本

from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex, Settings
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
import time


# ➊ 配置全局设置（替代 ServiceContext）
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)

# ➋ 连接 Qdrant - 使用与test_qdrant_connection.py相同的配置
qdrant = QdrantClient(
    host="localhost",
    port=6334,  # gRPC 端口
    prefer_grpc=True,
    timeout=10
)
print("✅ 客户端创建成功")

# 检查并创建集合
collection_name = "rw_ai_lms"
if not qdrant.collection_exists(collection_name):
    qdrant.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
    )
    print(f"✅ 集合 '{collection_name}' 创建成功")
else:
    print(f"✅ 集合 '{collection_name}' 已存在")

vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant)
storage_context = StorageContext.from_defaults(vector_store=vector_store)

# ➌ 建索引（不再需要 service_context 参数）
index = VectorStoreIndex(nodes, storage_context=storage_context)

print("✅ 已写入", len(nodes), "条向量")

# 直接从已有的 Qdrant 数据进行向量检索和问答

from qdrant_client import QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex, Settings
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding

# 配置全局设置
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)

# 连接到已有的 Qdrant 集合
qdrant = QdrantClient(
    host="localhost",
    port=6334,
    prefer_grpc=True,
    timeout=10
)

# 从已有集合创建向量存储
collection_name = "rw_ai_lms"  # 使用已有的集合
vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant)
storage_context = StorageContext.from_defaults(vector_store=vector_store)

# 从已有数据创建索引（不需要重新写入数据）
index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)

# 创建查询引擎
query_engine = index.as_query_engine(
    similarity_top_k=3,  # 返回最相似的3个结果
    response_mode="compact"  # 紧凑回答模式
)

# 测试查询
questions = [
    "课程材料的核心概念是什么？",
    "这些文档主要讲了什么内容？",
    "有哪些重要的知识点？"
]

print("🔍 开始向量检索问答...")
for i, question in enumerate(questions, 1):
    print(f"\n--- 问题 {i}: {question} ---")
    try:
        response = query_engine.query(question)
        print(f"回答: {response}")
        
        # 显示检索到的源文档信息
        if hasattr(response, 'source_nodes'):
                print(f"\n📚 参考了 {len(response.source_nodes)} 个文档片段")
                for j, node in enumerate(response.source_nodes, 1):
                    score = getattr(node, 'score', 'N/A')
                    print(f"  片段{j} (相似度: {score:.4f}): {node.text[:100]}...")
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    
    print("=" * 60)

query_engine = index.as_query_engine(similarity_top_k=3)
question = "你该课程材料核心概念是什么？"
response = query_engine.query(question)
print(response)

# 获取提示词

prompts = query_engine.get_prompts()
for key, prompt in prompts.items():
    print(f"--- Prompt Key: {key} ---")
    print(prompt.get_template())
    print("\n")

# 定制化提示词，并且获得定制提示词的回答
import llama_index.core
from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, PromptTemplate

# 1. 用之前的代码块建立的索引

# 2. 定义你自己的提示词模板
# 你可以修改指令、语气、增加约束等
custom_qa_template_str = (
    "你是类似理查德·费曼的编程教育家📚，你将根据以下【参考文本】来写一个尽可能简短的教程讲解【用户想要理解的知识】🧐。在讲解中请多使用emoji，让内容生动有趣，并保持排版简单、清晰、易读 ✅。\n\n"
    "----------【参考文本】开始-----------\n\n"
    "{context_str}\n\n"
    "----------【参考文本】结束-----------\n\n"
    "-----------【用户想要理解的知识】开始-----------\n\n"
    "{query_str}\n\n"
    "-----------【用户想要理解的知识】结束-----------\n\n"
    "回答："
)
custom_qa_prompt = PromptTemplate(custom_qa_template_str)

print(f"用PromptTemplate加工过的qa_prompt: {custom_qa_prompt.get_template()}")


# 3. 在创建查询引擎时，使用 `text_qa_template` 参数传入你的定制化模板
query_engine = index.as_query_engine(
    similarity_top_k=10,
    text_qa_template=custom_qa_prompt
)

# 4. 执行查询，此时会使用你的新提示词
question = "# 函数的核心概念与实践 ## 函数定义与调用 ### def 关键字定义函数，函数名紧随其后，小括号内列出必要形参。### 函数体由缩进代码块组成，可包含文档字符串描述功能。### 调用函数时写函数名并加小括号，必要时传入实参。### 无需参数的函数定义和调用括号不可省略。### 函数体通常执行特定任务或显示信息。"
response = query_engine.query(question)

# 打印最终结果
print("\n--- 定制化提示词后的回答 ---")
print(response)