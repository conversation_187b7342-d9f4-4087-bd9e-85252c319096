[{"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "glob", "kind": 6, "isExtraImport": true, "importPath": "glob", "description": "glob", "detail": "glob", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "sysconfig", "kind": 6, "isExtraImport": true, "importPath": "sysconfig", "description": "sysconfig", "detail": "sysconfig", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "winreg", "kind": 6, "isExtraImport": true, "importPath": "winreg", "description": "winreg", "detail": "winreg", "documentation": {}}, {"label": "site", "kind": 6, "isExtraImport": true, "importPath": "site", "description": "site", "detail": "site", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "llama_index.core", "kind": 6, "isExtraImport": true, "importPath": "llama_index.core", "description": "llama_index.core", "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Document", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Document", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Document", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Document", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "StorageContext", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "VectorStoreIndex", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "Settings", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "PromptTemplate", "importPath": "llama_index.core", "description": "llama_index.core", "isExtraImport": true, "detail": "llama_index.core", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "OpenAI", "importPath": "llama_index.llms.openai", "description": "llama_index.llms.openai", "isExtraImport": true, "detail": "llama_index.llms.openai", "documentation": {}}, {"label": "OpenAIEmbedding", "importPath": "llama_index.embeddings.openai", "description": "llama_index.embeddings.openai", "isExtraImport": true, "detail": "llama_index.embeddings.openai", "documentation": {}}, {"label": "OpenAIEmbedding", "importPath": "llama_index.embeddings.openai", "description": "llama_index.embeddings.openai", "isExtraImport": true, "detail": "llama_index.embeddings.openai", "documentation": {}}, {"label": "OpenAIEmbedding", "importPath": "llama_index.embeddings.openai", "description": "llama_index.embeddings.openai", "isExtraImport": true, "detail": "llama_index.embeddings.openai", "documentation": {}}, {"label": "OpenAIEmbedding", "importPath": "llama_index.embeddings.openai", "description": "llama_index.embeddings.openai", "isExtraImport": true, "detail": "llama_index.embeddings.openai", "documentation": {}}, {"label": "OpenAIEmbedding", "importPath": "llama_index.embeddings.openai", "description": "llama_index.embeddings.openai", "isExtraImport": true, "detail": "llama_index.embeddings.openai", "documentation": {}}, {"label": "ChatSummaryMemoryBuffer", "importPath": "llama_index.core.memory", "description": "llama_index.core.memory", "isExtraImport": true, "detail": "llama_index.core.memory", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "llama_index.core.memory", "description": "llama_index.core.memory", "isExtraImport": true, "detail": "llama_index.core.memory", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "llama_index.core.memory", "description": "llama_index.core.memory", "isExtraImport": true, "detail": "llama_index.core.memory", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "importPath": "llama_index.core.memory", "description": "llama_index.core.memory", "isExtraImport": true, "detail": "llama_index.core.memory", "documentation": {}}, {"label": "ChatSummaryMemoryBuffer", "importPath": "llama_index.core.memory", "description": "llama_index.core.memory", "isExtraImport": true, "detail": "llama_index.core.memory", "documentation": {}}, {"label": "SimpleChatEngine", "importPath": "llama_index.core.chat_engine", "description": "llama_index.core.chat_engine", "isExtraImport": true, "detail": "llama_index.core.chat_engine", "documentation": {}}, {"label": "SimpleChatEngine", "importPath": "llama_index.core.chat_engine", "description": "llama_index.core.chat_engine", "isExtraImport": true, "detail": "llama_index.core.chat_engine", "documentation": {}}, {"label": "PromptTemplate", "importPath": "llama_index.core.prompts", "description": "llama_index.core.prompts", "isExtraImport": true, "detail": "llama_index.core.prompts", "documentation": {}}, {"label": "QdrantVectorStore", "importPath": "llama_index.vector_stores.qdrant", "description": "llama_index.vector_stores.qdrant", "isExtraImport": true, "detail": "llama_index.vector_stores.qdrant", "documentation": {}}, {"label": "QdrantVectorStore", "importPath": "llama_index.vector_stores.qdrant", "description": "llama_index.vector_stores.qdrant", "isExtraImport": true, "detail": "llama_index.vector_stores.qdrant", "documentation": {}}, {"label": "QdrantVectorStore", "importPath": "llama_index.vector_stores.qdrant", "description": "llama_index.vector_stores.qdrant", "isExtraImport": true, "detail": "llama_index.vector_stores.qdrant", "documentation": {}}, {"label": "QdrantVectorStore", "importPath": "llama_index.vector_stores.qdrant", "description": "llama_index.vector_stores.qdrant", "isExtraImport": true, "detail": "llama_index.vector_stores.qdrant", "documentation": {}}, {"label": "QdrantVectorStore", "importPath": "llama_index.vector_stores.qdrant", "description": "llama_index.vector_stores.qdrant", "isExtraImport": true, "detail": "llama_index.vector_stores.qdrant", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "RedisChatStore", "importPath": "llama_index.storage.chat_store.redis", "description": "llama_index.storage.chat_store.redis", "isExtraImport": true, "detail": "llama_index.storage.chat_store.redis", "documentation": {}}, {"label": "RedisChatStore", "importPath": "llama_index.storage.chat_store.redis", "description": "llama_index.storage.chat_store.redis", "isExtraImport": true, "detail": "llama_index.storage.chat_store.redis", "documentation": {}}, {"label": "Metadata<PERSON><PERSON>er", "importPath": "llama_index.core.vector_stores", "description": "llama_index.core.vector_stores", "isExtraImport": true, "detail": "llama_index.core.vector_stores", "documentation": {}}, {"label": "MetadataFilters", "importPath": "llama_index.core.vector_stores", "description": "llama_index.core.vector_stores", "isExtraImport": true, "detail": "llama_index.core.vector_stores", "documentation": {}}, {"label": "FilterOperator", "importPath": "llama_index.core.vector_stores", "description": "llama_index.core.vector_stores", "isExtraImport": true, "detail": "llama_index.core.vector_stores", "documentation": {}}, {"label": "OPENAI_CONFIG", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "QDRANT_CONFIG", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "REDIS_CONFIG", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "MEMORY_CONFIG", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "PROMPTS", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "UI_CONFIG", "importPath": "config", "description": "config", "isExtraImport": true, "detail": "config", "documentation": {}}, {"label": "traceback", "kind": 6, "isExtraImport": true, "importPath": "traceback", "description": "traceback", "detail": "traceback", "documentation": {}}, {"label": "<PERSON><PERSON>", "kind": 6, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "class Tee:\n    def __init__(self, file):\n        self.f = file\n    def write(self, what):\n        if self.f is not None:\n            try:\n                self.f.write(what.replace(\"\\n\", \"\\r\\n\"))\n            except OSError:\n                pass\n        tee_f.write(what)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_root_hkey", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU\n        return winreg.HKEY_CURRENT_USER", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "create_shortcut", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def create_shortcut(\n    path, description, filename, arguments=\"\", workdir=\"\", iconpath=\"\", iconindex=0\n):\n    import pythoncom\n    from win32com.shell import shell\n    ilink = pythoncom.CoCreateInstance(\n        shell.CLSID_ShellLink,\n        None,\n        pythoncom.CLSCTX_INPROC_SERVER,\n        shell.IID_IShellLink,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_special_folder_path", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_special_folder_path(path_name):\n    from win32com.shell import shell, shellcon\n    for maybe in \"\"\"\n        CSIDL_COMMON_STARTMENU CSIDL_STARTMENU CSIDL_COMMON_APPDATA\n        CSIDL_LOCAL_APPDATA CSIDL_APPDATA CSIDL_COMMON_DESKTOPDIRECTORY\n        CSIDL_DESKTOPDIRECTORY CSIDL_COMMON_STARTUP CSIDL_STARTUP\n        CSIDL_COMMON_PROGRAMS CSIDL_PROGRAMS CSIDL_PROGRAM_FILES_COMMON\n        CSIDL_PROGRAM_FILES CSIDL_FONTS\"\"\".split():\n        if maybe == path_name:\n            csidl = getattr(shellcon, maybe)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "CopyTo", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def CopyTo(desc, src, dest):\n    import win32api\n    import win32con\n    while 1:\n        try:\n            win32api.CopyFile(src, dest, 0)\n            return\n        except win32api.error as details:\n            if details.winerror == 5:  # access denied - user not admin.\n                raise", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "LoadSystemModule", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def LoadSystemModule(lib_dir, modname):\n    # See if this is a debug build.\n    import importlib.machinery\n    import importlib.util\n    suffix = \"_d\" if \"_d.pyd\" in importlib.machinery.EXTENSION_SUFFIXES else \"\"\n    filename = \"%s%d%d%s.dll\" % (\n        modname,\n        sys.version_info.major,\n        sys.version_info.minor,\n        suffix,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "SetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def SetPyKeyVal(key_name, value_name, value):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.CreateKey(root_key, key_name)\n        try:\n            winreg.SetValueEx(my_key, value_name, 0, winreg.REG_SZ, value)\n            if verbose:\n                print(f\"-> {root_key_name}\\\\{key_name}[{value_name}]={value!r}\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "UnsetPyKeyVal", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def UnsetPyKeyVal(key_name, value_name, delete_key=False):\n    root_hkey = get_root_hkey()\n    root_key = winreg.OpenKey(root_hkey, root_key_name)\n    try:\n        my_key = winreg.OpenKey(root_key, key_name, 0, winreg.KEY_SET_VALUE)\n        try:\n            winreg.DeleteValue(my_key, value_name)\n            if verbose:\n                print(f\"-> DELETE {root_key_name}\\\\{key_name}[{value_name}]\")\n        finally:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterCOMObjects", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterCOMObjects(register=True):\n    import win32com.server.register\n    if register:\n        func = win32com.server.register.RegisterClasses\n    else:\n        func = win32com.server.register.UnregisterClasses\n    flags = {}\n    if not verbose:\n        flags[\"quiet\"] = 1\n    for module, klass_name in com_modules:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterHelpFile", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterHelpFile(register=True, lib_dir=None):\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    if register:\n        # Register the .chm help file.\n        chm_file = os.path.join(lib_dir, \"PyWin32.chm\")\n        if os.path.isfile(chm_file):\n            # This isn't recursive, so if 'Help' doesn't exist, we croak\n            SetPyKeyVal(\"Help\", None, None)\n            SetPyKeyVal(\"Help\\\\Pythonwin Reference\", None, chm_file)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "RegisterPyt<PERSON><PERSON>", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def RegisterPythonwin(register=True, lib_dir=None):\n    \"\"\"Add (or remove) Pythonwin to context menu for python scripts.\n    ??? Should probably also add Edit command for pys files also.\n    Also need to remove these keys on uninstall, but there's no function\n    to add registry entries to uninstall log ???\n    \"\"\"\n    import os\n    if lib_dir is None:\n        lib_dir = sysconfig.get_paths()[\"platlib\"]\n    classes_root = get_root_hkey()", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_shortcuts_folder", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_shortcuts_folder():\n    if get_root_hkey() == winreg.HKEY_LOCAL_MACHINE:\n        try:\n            fldr = get_special_folder_path(\"CSIDL_COMMON_PROGRAMS\")\n        except OSError:\n            # No CSIDL_COMMON_PROGRAMS on this platform\n            fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")\n    else:\n        # non-admin install - always goes in this user's start menu.\n        fldr = get_special_folder_path(\"CSIDL_PROGRAMS\")", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "get_system_dir", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def get_system_dir():\n    import win32api  # we assume this exists.\n    try:\n        import pythoncom\n        import win32process\n        from win32com.shell import shell, shellcon\n        try:\n            if win32process.IsWow64Process():\n                return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEMX86)\n            return shell.SHGetSpecialFolderPath(0, shellcon.CSIDL_SYSTEM)", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "fixup_dbi", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def fixup_dbi():\n    # We used to have a dbi.pyd with our .pyd files, but now have a .py file.\n    # If the user didn't uninstall, they will find the .pyd which will cause\n    # problems - so handle that.\n    import win32api\n    import win32con\n    pyd_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi.pyd\")\n    pyd_d_name = os.path.join(os.path.dirname(win32api.__file__), \"dbi_d.pyd\")\n    py_name = os.path.join(os.path.dirname(win32con.__file__), \"dbi.py\")\n    for this_pyd in (pyd_name, pyd_d_name):", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "install", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def install(lib_dir):\n    import traceback\n    # The .pth file is now installed as a regular file.\n    # Create the .pth file in the site-packages dir, and use only relative paths\n    # We used to write a .pth directly to sys.prefix - clobber it.\n    if os.path.isfile(os.path.join(sys.prefix, \"pywin32.pth\")):\n        os.unlink(os.path.join(sys.prefix, \"pywin32.pth\"))\n    # The .pth may be new and therefore not loaded in this session.\n    # Setup the paths just in case.\n    for name in \"win32 win32\\\\lib Pythonwin\".split():", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "uninstall", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def uninstall(lib_dir):\n    # First ensure our system modules are loaded from pywin32_system, so\n    # we can remove the ones we copied...\n    LoadSystemModule(lib_dir, \"pywintypes\")\n    LoadSystemModule(lib_dir, \"pythoncom\")\n    try:\n        RegisterCOMObjects(False)\n    except Exception as why:\n        print(f\"Failed to unregister COM objects: {why}\")\n    try:", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verify_destination", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def verify_destination(location: str) -> str:\n    location = os.path.abspath(location)\n    if not os.path.isdir(location):\n        raise argparse.ArgumentTypeError(\n            f'Path \"{location}\" is not an existing directory!'\n        )\n    return location\ndef main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "def main():\n    parser = argparse.ArgumentParser(\n        formatter_class=argparse.RawDescriptionHelpFormatter,\n        description=\"\"\"A post-install script for the pywin32 extensions.\n    * Typical usage:\n    > python -m pywin32_postinstall -install\n    * or (shorter but you don't have control over which python environment is used)\n    > pywin32_postinstall -install\n    You need to execute this script, with a '-install' parameter,\n    to ensure the environment is setup correctly to install COM objects, services, etc.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "tee_f", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "tee_f = open(\n    os.path.join(\n        tempfile.gettempdir(),  # Send output somewhere so it can be found if necessary...\n        \"pywin32_postinstall.log\",\n    ),\n    \"w\",\n)\nclass Tee:\n    def __init__(self, file):\n        self.f = file", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stderr", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stderr = <PERSON><PERSON>(sys.stderr)\nsys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "sys.stdout", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "sys.stdout = Tee(sys.stdout)\ncom_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "com_modules", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "com_modules = [\n    # module_name,                      class_names\n    (\"win32com.servers.interp\", \"Interpreter\"),\n    (\"win32com.servers.dictionary\", \"DictionaryPolicy\"),\n    (\"win32com.axscript.client.pyscript\", \"PyScript\"),\n]\n# Is this a 'silent' install - ie, avoid all dialogs.\n# Different than 'verbose'\nsilent = 0\n# Verbosity of output messages.", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "silent", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "silent = 0\n# Verbosity of output messages.\nverbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "verbose", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "verbose = 1\nroot_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "root_key_name", "kind": 5, "importPath": ".venv.Scripts.pywin32_postinstall", "description": ".venv.Scripts.pywin32_postinstall", "peekOfCode": "root_key_name = \"Software\\\\Python\\\\PythonCore\\\\\" + sys.winver\ndef get_root_hkey():\n    try:\n        winreg.OpenKey(\n            winreg.HKEY_LOCAL_MACHINE, root_key_name, 0, winreg.KEY_CREATE_SUB_KEY\n        )\n        return winreg.HKEY_LOCAL_MACHINE\n    except OSError:\n        # Either not exist, or no permissions to create subkey means\n        # must be HKCU", "detail": ".venv.Scripts.pywin32_postinstall", "documentation": {}}, {"label": "run_test", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()\n    result = subprocess.run(cmd, check=False, cwd=dirname)\n    print(f\"*** Test script '{script}' exited with {result.returncode}\")\n    sys.stdout.flush()\n    if result.returncode:", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "find_and_run", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def find_and_run(possible_locations, extras):\n    for maybe in possible_locations:\n        if os.path.isfile(maybe):\n            run_test(maybe, extras)\n            break\n    else:\n        raise RuntimeError(\n            \"Failed to locate a test script in one of %s\" % possible_locations\n        )\ndef main():", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "main", "kind": 2, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "def main():\n    import argparse\n    code_directories = [project_root] + site_packages\n    parser = argparse.ArgumentParser(\n        description=\"A script to trigger tests in all subprojects of PyWin32.\"\n    )\n    parser.add_argument(\n        \"-no-user-interaction\",\n        default=False,\n        action=\"store_true\",", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))\nsite_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "site_packages", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "site_packages = [site.getusersitepackages()] + site.getsitepackages()\nfailures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "failures", "kind": 5, "importPath": ".venv.Scripts.pywin32_testall", "description": ".venv.Scripts.pywin32_testall", "peekOfCode": "failures = []\n# Run a test using subprocess and wait for the result.\n# If we get an returncode != 0, we know that there was an error, but we don't\n# abort immediately - we run as many tests as we can.\ndef run_test(script, cmdline_extras):\n    dirname, scriptname = os.path.split(script)\n    # some tests prefer to be run from their directory.\n    cmd = [sys.executable, \"-u\", scriptname] + cmdline_extras\n    print(\"--- Running '%s' ---\" % script)\n    sys.stdout.flush()", "detail": ".venv.Scripts.pywin32_testall", "documentation": {}}, {"label": "os.environ[\"OPENAI_API_KEY\"]", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\nos.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "os.environ[\"OPENAI_BASE_URL\"]", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "Settings.llm", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "Settings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nprint(\"✅ 已设置 LLM 和 Embedding。\\n\")", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "Settings.embed_model", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "Settings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nprint(\"✅ 已设置 LLM 和 Embedding。\\n\")\n# 创建测试文档\ndocuments = [\n    Document(\n        text=\"Python函数定义使用def关键字，语法为：def function_name(parameters): return value\",\n        metadata={\"course_id\": \"course_01\", \"material_id\": \"material_001\"}", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "documents", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "documents = [\n    Document(\n        text=\"Python函数定义使用def关键字，语法为：def function_name(parameters): return value\",\n        metadata={\"course_id\": \"course_01\", \"material_id\": \"material_001\"}\n    ),\n    Document(\n        text=\"函数参数可以是位置参数、关键字参数、默认参数和可变参数(*args, **kwargs)\",\n        metadata={\"course_id\": \"course_01\", \"material_id\": \"material_002\"}\n    ),\n    Document(", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "client = QdrantClient(location=\":memory:\")\nvector_store = QdrantVectorStore(client=client, collection_name=\"test_docs\")\nindex = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"✅ 知识库已建立\\n\")\n# 创建共享内存\nchat_store = RedisChatStore(redis_url=\"redis://localhost:6379\", ttl=3600)\ncustom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n\"\"\"\nmemory = ChatSummaryMemoryBuffer.from_defaults(\n    token_limit=1000,", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "vector_store", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "vector_store = QdrantVectorStore(client=client, collection_name=\"test_docs\")\nindex = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"✅ 知识库已建立\\n\")\n# 创建共享内存\nchat_store = RedisChatStore(redis_url=\"redis://localhost:6379\", ttl=3600)\ncustom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n\"\"\"\nmemory = ChatSummaryMemoryBuffer.from_defaults(\n    token_limit=1000,\n    llm=Settings.llm,", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "index", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "index = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"✅ 知识库已建立\\n\")\n# 创建共享内存\nchat_store = RedisChatStore(redis_url=\"redis://localhost:6379\", ttl=3600)\ncustom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n\"\"\"\nmemory = ChatSummaryMemoryBuffer.from_defaults(\n    token_limit=1000,\n    llm=Settings.llm,\n    chat_store=chat_store,", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "chat_store", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "chat_store = RedisChatStore(redis_url=\"redis://localhost:6379\", ttl=3600)\ncustom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n\"\"\"\nmemory = ChatSummaryMemoryBuffer.from_defaults(\n    token_limit=1000,\n    llm=Settings.llm,\n    chat_store=chat_store,\n    chat_store_key=\"debug_chat\",\n    summarize_prompt=custom_summary_prompt\n)", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "custom_summary_prompt", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "custom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n\"\"\"\nmemory = ChatSummaryMemoryBuffer.from_defaults(\n    token_limit=1000,\n    llm=Settings.llm,\n    chat_store=chat_store,\n    chat_store_key=\"debug_chat\",\n    summarize_prompt=custom_summary_prompt\n)\nprint(\"✅ 共享内存已创建\\n\")", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "memory", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "memory = ChatSummaryMemoryBuffer.from_defaults(\n    token_limit=1000,\n    llm=Settings.llm,\n    chat_store=chat_store,\n    chat_store_key=\"debug_chat\",\n    summarize_prompt=custom_summary_prompt\n)\nprint(\"✅ 共享内存已创建\\n\")\n# 创建condense_plus_context引擎\nnew_condense_prompt = PromptTemplate(", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "new_condense_prompt", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "new_condense_prompt = PromptTemplate(\n    \"你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\\n\"\n    \"注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\\n\"\n    \"=== 聊天历史 ===\\n\"\n    \"{chat_history}\\n\\n\"\n    \"=== 学生最新提出的问题 ===\\n\"\n    \"{question}\\n\\n\"\n    \"=== 改写后的独立问题 ===\\n\"\n)\ncustom_context_prompt = (", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "custom_context_prompt", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "custom_context_prompt = (\n    \"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\\n\\n\"\n    \"📚 **相关文档内容：**\\n\"\n    \"{context_str}\\n\\n\"\n    \"🎯 **回答要求：**\\n\"\n    \"1. 严格基于上述文档内容进行回答\\n\"\n    \"2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\\n\"\n    \"3. 回答要条理清晰，使用适当的emoji让内容更生动\\n\"\n    \"4. 请引用具体的文档内容来支撑你的回答\\n\\n\"\n    \"💡 **请基于以上文档和之前的对话历史来回答用户的问题。**\"", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "condense_question_plus_engine", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "condense_question_plus_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",\n    condense_prompt=new_condense_prompt,\n    context_prompt=custom_context_prompt,\n    memory=memory,\n    # system_prompt=\"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\",\n    verbose=True,\n)\n# 创建simple引擎\nsimple_engine = SimpleChatEngine.from_defaults(", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "simple_engine", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "simple_engine = SimpleChatEngine.from_defaults(\n    llm=Settings.llm,\n    memory=memory,\n    system_prompt=\"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\",\n    verbose=True\n)\nprint(\"✅ 两个引擎均已就绪\\n\")\n# 交互式对话终端\nprint(\"\\n\" + \"=\"*90)\nprint(\"🎯 欢迎使用LLM调试终端！\")", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "current_engine", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "current_engine = condense_question_plus_engine\ncurrent_mode = \"condense_plus_context\"\nwhile True:\n    mode_display = \"🔎 RAG检索\" if current_mode == \"condense_plus_context\" else \"💬 纯对话\"\n    user_input = input(f\"[{mode_display}] 👤 你: \").strip()\n    if not user_input:\n        continue\n    # 处理命令\n    if user_input == \"1\":\n        current_engine = condense_question_plus_engine", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "current_mode", "kind": 5, "importPath": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "description": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "peekOfCode": "current_mode = \"condense_plus_context\"\nwhile True:\n    mode_display = \"🔎 RAG检索\" if current_mode == \"condense_plus_context\" else \"💬 纯对话\"\n    user_input = input(f\"[{mode_display}] 👤 你: \").strip()\n    if not user_input:\n        continue\n    # 处理命令\n    if user_input == \"1\":\n        current_engine = condense_question_plus_engine\n        current_mode = \"condense_plus_context\"", "detail": "add_backend_history.llm_debug_terminal_llama_index_shared_memory_redis_storage", "documentation": {}}, {"label": "os.environ[\"OPENAI_API_KEY\"]", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\n# 如需自定义网关（如代理或 Azure），取消下面注释并替换\nos.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n# ➊ 配置全局设置（替代 ServiceContext）\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "os.environ[\"OPENAI_BASE_URL\"]", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n# ➊ 配置全局设置（替代 ServiceContext）\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "Settings.llm", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "Settings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nfrom llama_index.core import Document, VectorStoreIndex", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "Settings.embed_model", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "Settings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nfrom llama_index.core import Document, VectorStoreIndex\nfrom llama_index.vector_stores.qdrant import QdrantVectorStore\nfrom qdrant_client import QdrantClient\n# 准备知识库文档\ndocuments = [\n    Document(", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "documents", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "documents = [\n    Document(\n        text=\"FlexiBot的第一个核心功能是多语言翻译。它可以实时翻译超过50种语言，帮助用户跨越语言障碍进行交流。\",\n        metadata={\"doc_name\": \"功能介绍\", \"feature_id\": \"translation\"}\n    ),\n    Document(\n        text=\"FlexiBot的第二个能力是代码生成。它精通Python、JavaScript和SQL，可以根据用户的自然语言描述生成相应的代码片段。\",\n        metadata={\"doc_name\": \"功能介绍\", \"feature_id\": \"coding\"}\n    ),\n    Document(", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "client = QdrantClient(location=\":memory:\")\n# 创建一个 QdrantVectorStore 实例\nvector_store = QdrantVectorStore(client=client, collection_name=\"flexibot_docs\")\n# 从文档创建索引，数据将自动存入 Qdrant\nindex = VectorStoreIndex.from_documents(\n    documents,\n    vector_store=vector_store,\n)\nprint(\"知识库已成功索引到 Qdrant (内存模式)！\")\nfrom llama_index.core.memory import ChatMemoryBuffer", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "vector_store", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "vector_store = QdrantVectorStore(client=client, collection_name=\"flexibot_docs\")\n# 从文档创建索引，数据将自动存入 Qdrant\nindex = VectorStoreIndex.from_documents(\n    documents,\n    vector_store=vector_store,\n)\nprint(\"知识库已成功索引到 Qdrant (内存模式)！\")\nfrom llama_index.core.memory import ChatMemoryBuffer\n# 创建一个内存来存储对话历史\nmemory = ChatMemoryBuffer.from_defaults(token_limit=2000)", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "index", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "index = VectorStoreIndex.from_documents(\n    documents,\n    vector_store=vector_store,\n)\nprint(\"知识库已成功索引到 Qdrant (内存模式)！\")\nfrom llama_index.core.memory import ChatMemoryBuffer\n# 创建一个内存来存储对话历史\nmemory = ChatMemoryBuffer.from_defaults(token_limit=2000)\n# 从索引创建上下文聊天引擎\ncontext_chat_engine = index.as_chat_engine(", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "memory", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "memory = ChatMemoryBuffer.from_defaults(token_limit=2000)\n# 从索引创建上下文聊天引擎\ncontext_chat_engine = index.as_chat_engine(\n    # chat_mode=\"context\",\n    chat_mode=\"condense_question\", #总体看这个模式更加合理\n    # chat_mode=\"simple\", #这个是随便一问一答模式,不用知识库\n    # chat_mode=\"condense_plus_context\", #没看懂这个是干嘛的，感觉浪费资源\n    memory=memory,\n    # system_prompt=\"你是一个名叫 FlexiBot 的乐于助人的AI助手。请根据上下文回答问题。\",\n    verbose=True", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "context_chat_engine", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "context_chat_engine = index.as_chat_engine(\n    # chat_mode=\"context\",\n    chat_mode=\"condense_question\", #总体看这个模式更加合理\n    # chat_mode=\"simple\", #这个是随便一问一答模式,不用知识库\n    # chat_mode=\"condense_plus_context\", #没看懂这个是干嘛的，感觉浪费资源\n    memory=memory,\n    # system_prompt=\"你是一个名叫 FlexiBot 的乐于助人的AI助手。请根据上下文回答问题。\",\n    verbose=True\n)\n# 开始第一轮对话", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "user_message_1", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "user_message_1 = \"FlexiBot有什么功能？\"\nprint('-'*20,\"用户message start\",'-'*20)\nprint(f\"👤 用户: {user_message_1}\")\nprint('-'*20,\"用户message end\",'-'*20, \"\\n\\n\")\nresponse_1 = context_chat_engine.chat(user_message_1)\nprint('-'*20,\"AI response start\",'-'*20)\nprint(f\"🤖 FlexiBot: {response_1}\\n\")\nprint('-'*20,\"AI response end\",'-'*20, \"\\n\\n\")\n# 第二轮对话，提出一个依赖上一轮对话的内容\nuser_message_2 = \"详细介绍一下第二个功能。\"", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "response_1", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "response_1 = context_chat_engine.chat(user_message_1)\nprint('-'*20,\"AI response start\",'-'*20)\nprint(f\"🤖 FlexiBot: {response_1}\\n\")\nprint('-'*20,\"AI response end\",'-'*20, \"\\n\\n\")\n# 第二轮对话，提出一个依赖上一轮对话的内容\nuser_message_2 = \"详细介绍一下第二个功能。\"\nprint('-'*20,\"用户message start\",'-'*20)\nprint(f\"👤 用户: {user_message_2}\")\nprint('-'*20,\"用户message end\",'-'*20, \"\\n\\n\")\nresponse_2 = context_chat_engine.chat(user_message_2)", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "user_message_2", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "user_message_2 = \"详细介绍一下第二个功能。\"\nprint('-'*20,\"用户message start\",'-'*20)\nprint(f\"👤 用户: {user_message_2}\")\nprint('-'*20,\"用户message end\",'-'*20, \"\\n\\n\")\nresponse_2 = context_chat_engine.chat(user_message_2)\nprint('-'*20,\"AI response start\",'-'*20)\nprint(f\"🤖 FlexiBot: {response_2}\\n\")\nprint('-'*20,\"AI response end\",'-'*20, \"\\n\\n\")\nprint(\"--- 对话结束 ---\")", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "response_2", "kind": 5, "importPath": "proc_history_an_memory.chat_context_mode_verbose", "description": "proc_history_an_memory.chat_context_mode_verbose", "peekOfCode": "response_2 = context_chat_engine.chat(user_message_2)\nprint('-'*20,\"AI response start\",'-'*20)\nprint(f\"🤖 FlexiBot: {response_2}\\n\")\nprint('-'*20,\"AI response end\",'-'*20, \"\\n\\n\")\nprint(\"--- 对话结束 ---\")", "detail": "proc_history_an_memory.chat_context_mode_verbose", "documentation": {}}, {"label": "os.environ[\"OPENAI_API_KEY\"]", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\n# 如需自定义网关（如代理或 Azure），取消下面注释并替换\nos.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n# ➊ 配置全局设置（替代 ServiceContext）\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "os.environ[\"OPENAI_BASE_URL\"]", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n# ➊ 配置全局设置（替代 ServiceContext）\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "Settings.llm", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "Settings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\n# ========== 1) 构造一个“更详细”的 FlexiBot 产品知识库（12 条文档） ==========", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "Settings.embed_model", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "Settings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\n# ========== 1) 构造一个“更详细”的 FlexiBot 产品知识库（12 条文档） ==========\ndocuments = [\n    Document(\n        text=(\n            \"【产品总览】FlexiBot 提供三大核心功能：\\n\"\n            \"1) 多语言翻译（feature_id=1_translation）：双向实时翻译，覆盖 50+ 语言；\\n\"", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "documents", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "documents = [\n    Document(\n        text=(\n            \"【产品总览】FlexiBot 提供三大核心功能：\\n\"\n            \"1) 多语言翻译（feature_id=1_translation）：双向实时翻译，覆盖 50+ 语言；\\n\"\n            \"2) 代码生成（feature_id=2_coding）：将自然语言转为代码，主要覆盖 Python / JavaScript / SQL；\\n\"\n            \"3) 创意写作（feature_id=3_writing）：营销文案、诗歌、短篇等创作。\\n\"\n            \"本手册中的‘功能次序’以 feature_id 的序号为准：第一=翻译，第二=代码生成，第三=写作。\\n\"\n        ),\n        metadata={\"doc_name\": \"产品总览\", \"feature_id\": \"0_overview\", \"section\": \"overview\"}", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "client = QdrantClient(location=\":memory:\")\nvector_store = QdrantVectorStore(client=client, collection_name=\"flexibot_docs_detailed\")\nindex = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"\\n\" + \"=\"*72)\nprint(\"更详细的知识库已建立（Qdrant / 内存） ✅\")\nprint(\"=\"*72 + \"\\n\")\n# ========== 2) 建立聊天引擎 ==========\nmemory = ChatMemoryBuffer.from_defaults(token_limit=2000)\nchat_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "vector_store", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "vector_store = QdrantVectorStore(client=client, collection_name=\"flexibot_docs_detailed\")\nindex = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"\\n\" + \"=\"*72)\nprint(\"更详细的知识库已建立（Qdrant / 内存） ✅\")\nprint(\"=\"*72 + \"\\n\")\n# ========== 2) 建立聊天引擎 ==========\nmemory = ChatMemoryBuffer.from_defaults(token_limit=2000)\nchat_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",\n    memory=memory,", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "index", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "index = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"\\n\" + \"=\"*72)\nprint(\"更详细的知识库已建立（Qdrant / 内存） ✅\")\nprint(\"=\"*72 + \"\\n\")\n# ========== 2) 建立聊天引擎 ==========\nmemory = ChatMemoryBuffer.from_defaults(token_limit=2000)\nchat_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",\n    memory=memory,\n    verbose=True", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "memory", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "memory = ChatMemoryBuffer.from_defaults(token_limit=2000)\nchat_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",\n    memory=memory,\n    verbose=True\n)\n# ========== 3) 演示两轮对话 ==========\nprint(\"#\"*72)\nprint(\"A. 第 1 轮：开启话题（让知识库进场）\")\nprint(\"#\"*72 + \"\\n\")", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "chat_engine", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "chat_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",\n    memory=memory,\n    verbose=True\n)\n# ========== 3) 演示两轮对话 ==========\nprint(\"#\"*72)\nprint(\"A. 第 1 轮：开启话题（让知识库进场）\")\nprint(\"#\"*72 + \"\\n\")\nuser_1 = \"FlexiBot 都有哪些核心功能？\"", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "user_1", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "user_1 = \"FlexiBot 都有哪些核心功能？\"\nprint(\"👤 用户：\", user_1, \"\\n\")\nresp_1 = chat_engine.chat(user_1)\nprint(\"—\"*72)\nprint(\"🤖 答案（第 1 轮）\")\nprint(\"—\"*72)\nprint(str(resp_1).strip(), \"\\n\")\nif getattr(resp_1, \"source_nodes\", None):\n    print(\"📚 证据片段（命中 Top-K）\")\n    for i, sn in enumerate(resp_1.source_nodes, 1):", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "resp_1", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "resp_1 = chat_engine.chat(user_1)\nprint(\"—\"*72)\nprint(\"🤖 答案（第 1 轮）\")\nprint(\"—\"*72)\nprint(str(resp_1).strip(), \"\\n\")\nif getattr(resp_1, \"source_nodes\", None):\n    print(\"📚 证据片段（命中 Top-K）\")\n    for i, sn in enumerate(resp_1.source_nodes, 1):\n        preview = sn.node.get_content().strip().replace(\"\\n\", \" \")\n        if len(preview) > 160:", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "user_2", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "user_2 = \"详细介绍一下第二个功能。\"\nprint(\"👤 用户：\", user_2, \"\\n\")\nresp_2 = chat_engine.chat(user_2)\nprint(\"—\"*72)\nprint(\"🤖 答案（第 2 轮）\")\nprint(\"—\"*72)\nprint(str(resp_2).strip(), \"\\n\")\nif getattr(resp_2, \"source_nodes\", None):\n    print(\"📚 证据片段（命中 Top-K）\")\n    for i, sn in enumerate(resp_2.source_nodes, 1):", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "resp_2", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_long", "description": "proc_history_an_memory.condense_plus_context_demo_long", "peekOfCode": "resp_2 = chat_engine.chat(user_2)\nprint(\"—\"*72)\nprint(\"🤖 答案（第 2 轮）\")\nprint(\"—\"*72)\nprint(str(resp_2).strip(), \"\\n\")\nif getattr(resp_2, \"source_nodes\", None):\n    print(\"📚 证据片段（命中 Top-K）\")\n    for i, sn in enumerate(resp_2.source_nodes, 1):\n        preview = sn.node.get_content().strip().replace(\"\\n\", \" \")\n        if len(preview) > 160:", "detail": "proc_history_an_memory.condense_plus_context_demo_long", "documentation": {}}, {"label": "os.environ[\"OPENAI_API_KEY\"]", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\n# 如需自定义网关（如代理或 Azure），取消下面注释并替换\nos.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n# ➊ 配置全局设置（替代 ServiceContext）\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "os.environ[\"OPENAI_BASE_URL\"]", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n# ➊ 配置全局设置（替代 ServiceContext）\nSettings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "Settings.llm", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "Settings.llm = OpenAI(\n    model=\"gpt-4o-mini\", \n    temperature=0.1,\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\n# ========== 1) 构造一个极小的“产品说明”知识库（三条文档） ==========", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "Settings.embed_model", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "Settings.embed_model = OpenAIEmbedding(\n    model=\"text-embedding-3-small\",\n    api_base=\"https://api.openai-proxy.org/v1\"\n)\n# ========== 1) 构造一个极小的“产品说明”知识库（三条文档） ==========\ndocuments = [\n    Document(\n        text=\"FlexiBot 的第 1 个核心功能是多语言翻译：支持 50+ 语言的双向实时翻译。\",\n        metadata={\"doc_name\": \"功能总览\", \"feature_id\": \"1_translation\"}\n    ),", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "documents", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "documents = [\n    Document(\n        text=\"FlexiBot 的第 1 个核心功能是多语言翻译：支持 50+ 语言的双向实时翻译。\",\n        metadata={\"doc_name\": \"功能总览\", \"feature_id\": \"1_translation\"}\n    ),\n    Document(\n        text=\"FlexiBot 的第 2 个核心功能是代码生成：精通 Python / JavaScript / SQL，能把自然语言转成代码片段。\",\n        metadata={\"doc_name\": \"功能总览\", \"feature_id\": \"2_coding\"}\n    ),\n    Document(", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "client = QdrantClient(location=\":memory:\")\nvector_store = QdrantVectorStore(client=client, collection_name=\"flexibot_docs\")\nindex = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"\\n\" + \"=\"*72)\nprint(\"知识库已建立（Qdrant / 内存） ✅\")\nprint(\"=\"*72 + \"\\n\")\n# ========== 2) 建立聊天引擎：condense_plus_context ==========\n# 解释：相比 condense_question 只把“独立问题”喂给“查询引擎”再作答，\n# condense_plus_context 会在最终回答阶段把“检索到的上下文 + 用户原话”都交给 LLM，\n# 因此既能延续对话上下文，又能利用检索到的事实依据。", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "vector_store", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "vector_store = QdrantVectorStore(client=client, collection_name=\"flexibot_docs\")\nindex = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"\\n\" + \"=\"*72)\nprint(\"知识库已建立（Qdrant / 内存） ✅\")\nprint(\"=\"*72 + \"\\n\")\n# ========== 2) 建立聊天引擎：condense_plus_context ==========\n# 解释：相比 condense_question 只把“独立问题”喂给“查询引擎”再作答，\n# condense_plus_context 会在最终回答阶段把“检索到的上下文 + 用户原话”都交给 LLM，\n# 因此既能延续对话上下文，又能利用检索到的事实依据。\nmemory = ChatMemoryBuffer.from_defaults(token_limit=2000)", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "index", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "index = VectorStoreIndex.from_documents(documents, vector_store=vector_store)\nprint(\"\\n\" + \"=\"*72)\nprint(\"知识库已建立（Qdrant / 内存） ✅\")\nprint(\"=\"*72 + \"\\n\")\n# ========== 2) 建立聊天引擎：condense_plus_context ==========\n# 解释：相比 condense_question 只把“独立问题”喂给“查询引擎”再作答，\n# condense_plus_context 会在最终回答阶段把“检索到的上下文 + 用户原话”都交给 LLM，\n# 因此既能延续对话上下文，又能利用检索到的事实依据。\nmemory = ChatMemoryBuffer.from_defaults(token_limit=2000)\nchat_engine = index.as_chat_engine(", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "memory", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "memory = ChatMemoryBuffer.from_defaults(token_limit=2000)\nchat_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",\n    memory=memory,\n    verbose=True,  # 打开 verbose 以便打印检索/提示词等中间过程\n)\n# ========== 3) 演示两轮对话：第 2 轮使用“指代” ==========\\n\nprint(\"#\"*72)\nprint(\"A. 第 1 轮：开启话题（让知识库进场）\")\nprint(\"#\"*72 + \"\\n\")", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "chat_engine", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "chat_engine = index.as_chat_engine(\n    chat_mode=\"condense_plus_context\",\n    memory=memory,\n    verbose=True,  # 打开 verbose 以便打印检索/提示词等中间过程\n)\n# ========== 3) 演示两轮对话：第 2 轮使用“指代” ==========\\n\nprint(\"#\"*72)\nprint(\"A. 第 1 轮：开启话题（让知识库进场）\")\nprint(\"#\"*72 + \"\\n\")\nuser_1 = \"FlexiBot 都有哪些核心功能？\"", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "user_1", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "user_1 = \"FlexiBot 都有哪些核心功能？\"\nprint(\"👤 用户：\", user_1, \"\\n\")\nresp_1 = chat_engine.chat(user_1)\n# —— 打印一个紧凑且易读的答案区块\nprint(\"—\"*72)\nprint(\"🤖 答案（第 1 轮）\")\nprint(\"—\"*72)\nprint(str(resp_1).strip(), \"\\n\")\n# —— （可选）显示来源片段，帮助你确认检索是否命中\nif getattr(resp_1, \"source_nodes\", None):", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "resp_1", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "resp_1 = chat_engine.chat(user_1)\n# —— 打印一个紧凑且易读的答案区块\nprint(\"—\"*72)\nprint(\"🤖 答案（第 1 轮）\")\nprint(\"—\"*72)\nprint(str(resp_1).strip(), \"\\n\")\n# —— （可选）显示来源片段，帮助你确认检索是否命中\nif getattr(resp_1, \"source_nodes\", None):\n    print(\"📚 证据片段（命中 Top-K）\")\n    for i, sn in enumerate(resp_1.source_nodes, 1):", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "user_2", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "user_2 = \"详细介绍一下第二个功能。\"\nprint(\"👤 用户：\", user_2, \"\\n\")\nresp_2 = chat_engine.chat(user_2)\nprint(\"—\"*72)\nprint(\"🤖 答案（第 2 轮）\")\nprint(\"—\"*72)\nprint(str(resp_2).strip(), \"\\n\")\nif getattr(resp_2, \"source_nodes\", None):\n    print(\"📚 证据片段（命中 Top-K）\")\n    for i, sn in enumerate(resp_2.source_nodes, 1):", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "resp_2", "kind": 5, "importPath": "proc_history_an_memory.condense_plus_context_demo_short", "description": "proc_history_an_memory.condense_plus_context_demo_short", "peekOfCode": "resp_2 = chat_engine.chat(user_2)\nprint(\"—\"*72)\nprint(\"🤖 答案（第 2 轮）\")\nprint(\"—\"*72)\nprint(str(resp_2).strip(), \"\\n\")\nif getattr(resp_2, \"source_nodes\", None):\n    print(\"📚 证据片段（命中 Top-K）\")\n    for i, sn in enumerate(resp_2.source_nodes, 1):\n        preview = sn.node.get_content().strip().replace(\"\\n\", \" \")\n        if len(preview) > 120:", "detail": "proc_history_an_memory.condense_plus_context_demo_short", "documentation": {}}, {"label": "OPENAI_CONFIG", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "OPENAI_CONFIG = {\n    \"api_key\": \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\",  # 请替换为您的API Key\n    \"base_url\": \"https://api.openai-proxy.org/v1\",  # 请根据需要修改\n    \"model\": \"gpt-4o-mini\",\n    \"temperature\": 0.1,\n    \"embedding_model\": \"text-embedding-3-small\"\n}\n# 🗄️ Qdrant向量数据库配置\nQDRANT_CONFIG = {\n    \"host\": \"localhost\",", "detail": "config", "documentation": {}}, {"label": "QDRANT_CONFIG", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "QDRANT_CONFIG = {\n    \"host\": \"localhost\",\n    \"port\": 6334,  # gRPC端口\n    \"prefer_grpc\": True,\n    \"timeout\": 10,\n    \"collection_name\": \"course_materials\"  # 请根据您的实际集合名称修改\n}\n# 💾 Redis配置\nREDIS_CONFIG = {\n    \"redis_url\": \"redis://localhost:6379\",", "detail": "config", "documentation": {}}, {"label": "REDIS_CONFIG", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "REDIS_CONFIG = {\n    \"redis_url\": \"redis://localhost:6379\",\n    \"ttl\": 3600  # 对话存活时间（秒）\n}\n# 🧠 Memory配置\nMEMORY_CONFIG = {\n    \"token_limit\": 4000,  # 内存token限制\n    \"summary_prompt\": \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n\"\"\"\n}", "detail": "config", "documentation": {}}, {"label": "MEMORY_CONFIG", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "MEMORY_CONFIG = {\n    \"token_limit\": 4000,  # 内存token限制\n    \"summary_prompt\": \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n\"\"\"\n}\n# 🎨 提示词配置\nPROMPTS = {\n    # condense_question用的提示词\n    \"condense_prompt\": \"\"\"你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\n注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。", "detail": "config", "documentation": {}}, {"label": "PROMPTS", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "PROMPTS = {\n    # condense_question用的提示词\n    \"condense_prompt\": \"\"\"你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\n注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\n=== 聊天历史 ===\n{chat_history}\n=== 学生最新提出的问题 ===\n{question}\n=== 改写后的独立问题 ===\n\"\"\",", "detail": "config", "documentation": {}}, {"label": "UI_CONFIG", "kind": 5, "importPath": "config", "description": "config", "peekOfCode": "UI_CONFIG = {\n    \"welcome_message\": \"\"\"\n🎯 欢迎使用智能聊天系统！\n📋 每次提问需要输入以下信息：\n   - conversation_id: 对话ID（用于标识和恢复对话）\n   - course_id: 课程ID（可选，与course_material_id互斥）\n   - course_material_id: 课程材料ID（可选，与course_id互斥）\n   - chat_engine_type: 聊天引擎类型\n     * condense_plus_context: RAG检索模式，会搜索相关文档\n     * simple: 纯对话模式，不进行文档检索", "detail": "config", "documentation": {}}, {"label": "create_memory_and_engines", "kind": 2, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "def create_memory_and_engines(conversation_id, course_id=None, course_material_id=None):\n    \"\"\"\n    🏗️ 根据用户输入创建memory和chat engines\n    Args:\n        conversation_id: 对话ID，用作Redis中的chat_store_key\n        course_id: 课程ID，用于过滤检索（与course_material_id互斥）\n        course_material_id: 课程材料ID，用于过滤检索（与course_id互斥）\n    Returns:\n        tuple: (memory, condense_question_plus_engine, simple_engine)\n    \"\"\"", "detail": "interactive_chat_system", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "def main():\n    \"\"\"\n    🎮 主交互循环\n    \"\"\"\n    print(\"\\n\" + \"=\"*80)\n    print(UI_CONFIG[\"welcome_message\"])\n    print(\"=\"*80 + \"\\n\")\n    while True:\n        try:\n            # 📝 获取用户输入", "detail": "interactive_chat_system", "documentation": {}}, {"label": "show_usage_example", "kind": 2, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "def show_usage_example():\n    \"\"\"\n    📖 显示使用示例\n    \"\"\"\n    print(\"\\n\" + \"=\"*60)\n    print(\"📖 使用示例：\")\n    print(\"=\"*60)\n    for example in UI_CONFIG[\"usage_examples\"]:\n        print(example[\"title\"])\n        print(f\"  conversation_id: {example['conversation_id']}\")", "detail": "interactive_chat_system", "documentation": {}}, {"label": "os.environ[\"OPENAI_API_KEY\"]", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "os.environ[\"OPENAI_API_KEY\"] = OPENAI_CONFIG[\"api_key\"]\nos.environ[\"OPENAI_BASE_URL\"] = OPENAI_CONFIG[\"base_url\"]\n# 设置全局LLM和嵌入模型\nSettings.llm = OpenAI(\n    model=OPENAI_CONFIG[\"model\"],\n    temperature=OPENAI_CONFIG[\"temperature\"],\n    api_base=OPENAI_CONFIG[\"base_url\"]\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=OPENAI_CONFIG[\"embedding_model\"],", "detail": "interactive_chat_system", "documentation": {}}, {"label": "os.environ[\"OPENAI_BASE_URL\"]", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "os.environ[\"OPENAI_BASE_URL\"] = OPENAI_CONFIG[\"base_url\"]\n# 设置全局LLM和嵌入模型\nSettings.llm = OpenAI(\n    model=OPENAI_CONFIG[\"model\"],\n    temperature=OPENAI_CONFIG[\"temperature\"],\n    api_base=OPENAI_CONFIG[\"base_url\"]\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=OPENAI_CONFIG[\"embedding_model\"],\n    api_base=OPENAI_CONFIG[\"base_url\"]", "detail": "interactive_chat_system", "documentation": {}}, {"label": "Settings.llm", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "Settings.llm = OpenAI(\n    model=OPENAI_CONFIG[\"model\"],\n    temperature=OPENAI_CONFIG[\"temperature\"],\n    api_base=OPENAI_CONFIG[\"base_url\"]\n)\nSettings.embed_model = OpenAIEmbedding(\n    model=OPENAI_CONFIG[\"embedding_model\"],\n    api_base=OPENAI_CONFIG[\"base_url\"]\n)\n# 🗄️ 连接Qdrant向量数据库", "detail": "interactive_chat_system", "documentation": {}}, {"label": "Settings.embed_model", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "Settings.embed_model = OpenAIEmbedding(\n    model=OPENAI_CONFIG[\"embedding_model\"],\n    api_base=OPENAI_CONFIG[\"base_url\"]\n)\n# 🗄️ 连接Qdrant向量数据库\nprint(\"📊 连接Qdrant向量数据库...\")\nqdrant_client = QdrantClient(\n    host=QDRANT_CONFIG[\"host\"],\n    port=QDRANT_CONFIG[\"port\"],\n    prefer_grpc=QDRANT_CONFIG[\"prefer_grpc\"],", "detail": "interactive_chat_system", "documentation": {}}, {"label": "qdrant_client", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "qdrant_client = QdrantClient(\n    host=QDRANT_CONFIG[\"host\"],\n    port=QDRANT_CONFIG[\"port\"],\n    prefer_grpc=QDRANT_CONFIG[\"prefer_grpc\"],\n    timeout=QDRANT_CONFIG[\"timeout\"]\n)\n# 从已有集合创建向量存储\ncollection_name = QDRANT_CONFIG[\"collection_name\"]\nvector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)\nstorage_context = StorageContext.from_defaults(vector_store=vector_store)", "detail": "interactive_chat_system", "documentation": {}}, {"label": "collection_name", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "collection_name = QDRANT_CONFIG[\"collection_name\"]\nvector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)\nstorage_context = StorageContext.from_defaults(vector_store=vector_store)\n# 从Qdrant向量存储创建index\nindex = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)\nprint(f\"✅ 已从Qdrant {QDRANT_CONFIG['port']}端口加载向量数据到index\")\n# 🧠 创建Redis聊天存储\nprint(\"💾 连接Redis聊天存储...\")\nchat_store = RedisChatStore(\n    redis_url=REDIS_CONFIG[\"redis_url\"],", "detail": "interactive_chat_system", "documentation": {}}, {"label": "vector_store", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)\nstorage_context = StorageContext.from_defaults(vector_store=vector_store)\n# 从Qdrant向量存储创建index\nindex = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)\nprint(f\"✅ 已从Qdrant {QDRANT_CONFIG['port']}端口加载向量数据到index\")\n# 🧠 创建Redis聊天存储\nprint(\"💾 连接Redis聊天存储...\")\nchat_store = RedisChatStore(\n    redis_url=REDIS_CONFIG[\"redis_url\"],\n    ttl=REDIS_CONFIG[\"ttl\"]", "detail": "interactive_chat_system", "documentation": {}}, {"label": "storage_context", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n# 从Qdrant向量存储创建index\nindex = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)\nprint(f\"✅ 已从Qdrant {QDRANT_CONFIG['port']}端口加载向量数据到index\")\n# 🧠 创建Redis聊天存储\nprint(\"💾 连接Redis聊天存储...\")\nchat_store = RedisChatStore(\n    redis_url=REDIS_CONFIG[\"redis_url\"],\n    ttl=REDIS_CONFIG[\"ttl\"]\n)", "detail": "interactive_chat_system", "documentation": {}}, {"label": "index", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)\nprint(f\"✅ 已从Qdrant {QDRANT_CONFIG['port']}端口加载向量数据到index\")\n# 🧠 创建Redis聊天存储\nprint(\"💾 连接Redis聊天存储...\")\nchat_store = RedisChatStore(\n    redis_url=REDIS_CONFIG[\"redis_url\"],\n    ttl=REDIS_CONFIG[\"ttl\"]\n)\n# 📝 自定义提示词模板（从配置文件读取）\nnew_condense_prompt = PromptTemplate(PROMPTS[\"condense_prompt\"])", "detail": "interactive_chat_system", "documentation": {}}, {"label": "chat_store", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "chat_store = RedisChatStore(\n    redis_url=REDIS_CONFIG[\"redis_url\"],\n    ttl=REDIS_CONFIG[\"ttl\"]\n)\n# 📝 自定义提示词模板（从配置文件读取）\nnew_condense_prompt = PromptTemplate(PROMPTS[\"condense_prompt\"])\ncustom_context_prompt = PROMPTS[\"context_prompt\"]\ncustom_summary_prompt = MEMORY_CONFIG[\"summary_prompt\"]\ndef create_memory_and_engines(conversation_id, course_id=None, course_material_id=None):\n    \"\"\"", "detail": "interactive_chat_system", "documentation": {}}, {"label": "new_condense_prompt", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "new_condense_prompt = PromptTemplate(PROMPTS[\"condense_prompt\"])\ncustom_context_prompt = PROMPTS[\"context_prompt\"]\ncustom_summary_prompt = MEMORY_CONFIG[\"summary_prompt\"]\ndef create_memory_and_engines(conversation_id, course_id=None, course_material_id=None):\n    \"\"\"\n    🏗️ 根据用户输入创建memory和chat engines\n    Args:\n        conversation_id: 对话ID，用作Redis中的chat_store_key\n        course_id: 课程ID，用于过滤检索（与course_material_id互斥）\n        course_material_id: 课程材料ID，用于过滤检索（与course_id互斥）", "detail": "interactive_chat_system", "documentation": {}}, {"label": "custom_context_prompt", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "custom_context_prompt = PROMPTS[\"context_prompt\"]\ncustom_summary_prompt = MEMORY_CONFIG[\"summary_prompt\"]\ndef create_memory_and_engines(conversation_id, course_id=None, course_material_id=None):\n    \"\"\"\n    🏗️ 根据用户输入创建memory和chat engines\n    Args:\n        conversation_id: 对话ID，用作Redis中的chat_store_key\n        course_id: 课程ID，用于过滤检索（与course_material_id互斥）\n        course_material_id: 课程材料ID，用于过滤检索（与course_id互斥）\n    Returns:", "detail": "interactive_chat_system", "documentation": {}}, {"label": "custom_summary_prompt", "kind": 5, "importPath": "interactive_chat_system", "description": "interactive_chat_system", "peekOfCode": "custom_summary_prompt = MEMORY_CONFIG[\"summary_prompt\"]\ndef create_memory_and_engines(conversation_id, course_id=None, course_material_id=None):\n    \"\"\"\n    🏗️ 根据用户输入创建memory和chat engines\n    Args:\n        conversation_id: 对话ID，用作Redis中的chat_store_key\n        course_id: 课程ID，用于过滤检索（与course_material_id互斥）\n        course_material_id: 课程材料ID，用于过滤检索（与course_id互斥）\n    Returns:\n        tuple: (memory, condense_question_plus_engine, simple_engine)", "detail": "interactive_chat_system", "documentation": {}}, {"label": "check_and_start", "kind": 2, "importPath": "start", "description": "start", "peekOfCode": "def check_and_start():\n    \"\"\"检查配置并启动系统\"\"\"\n    print(\"🔍 正在检查系统配置...\")\n    try:\n        # 运行配置测试\n        result = subprocess.run([sys.executable, \"test_config.py\"], \n                              capture_output=True, text=True)\n        print(result.stdout)\n        if result.stderr:\n            print(result.stderr)", "detail": "start", "documentation": {}}, {"label": "test_imports", "kind": 2, "importPath": "test_config", "description": "test_config", "peekOfCode": "def test_imports():\n    \"\"\"测试导入\"\"\"\n    print(\"🔍 测试Python包导入...\")\n    try:\n        from qdrant_client import QdrantClient\n        print(\"✅ qdrant_client 导入成功\")\n        from llama_index.vector_stores.qdrant import QdrantVectorStore\n        print(\"✅ llama_index.vector_stores.qdrant 导入成功\")\n        from llama_index.core import StorageContext, VectorStoreIndex, Settings, PromptTemplate\n        print(\"✅ llama_index.core 导入成功\")", "detail": "test_config", "documentation": {}}, {"label": "test_config", "kind": 2, "importPath": "test_config", "description": "test_config", "peekOfCode": "def test_config():\n    \"\"\"测试配置文件\"\"\"\n    print(\"\\n📋 测试配置文件...\")\n    try:\n        from config import OPENAI_CONFIG, QDRANT_CONFIG, REDIS_CONFIG, MEMORY_CONFIG, PROMPTS, UI_CONFIG\n        print(\"✅ 配置文件导入成功\")\n        # 检查必要的配置项\n        required_openai_keys = [\"api_key\", \"base_url\", \"model\", \"temperature\", \"embedding_model\"]\n        for key in required_openai_keys:\n            if key not in OPENAI_CONFIG:", "detail": "test_config", "documentation": {}}, {"label": "test_redis_connection", "kind": 2, "importPath": "test_config", "description": "test_config", "peekOfCode": "def test_redis_connection():\n    \"\"\"测试Redis连接\"\"\"\n    print(\"\\n💾 测试Redis连接...\")\n    try:\n        import redis\n        from config import REDIS_CONFIG\n        # 解析Redis URL\n        redis_url = REDIS_CONFIG[\"redis_url\"]\n        if redis_url.startswith(\"redis://\"):\n            host = redis_url.replace(\"redis://\", \"\").split(\":\")[0]", "detail": "test_config", "documentation": {}}, {"label": "test_qdrant_connection", "kind": 2, "importPath": "test_config", "description": "test_config", "peekOfCode": "def test_qdrant_connection():\n    \"\"\"测试Qdrant连接\"\"\"\n    print(\"\\n📊 测试Qdrant连接...\")\n    try:\n        from qdrant_client import QdrantClient\n        from config import QDRANT_CONFIG\n        client = QdrantClient(\n            host=QDRANT_CONFIG[\"host\"],\n            port=QDRANT_CONFIG[\"port\"],\n            prefer_grpc=QDRANT_CONFIG[\"prefer_grpc\"],", "detail": "test_config", "documentation": {}}, {"label": "test_openai_config", "kind": 2, "importPath": "test_config", "description": "test_config", "peekOfCode": "def test_openai_config():\n    \"\"\"测试OpenAI配置\"\"\"\n    print(\"\\n🤖 测试OpenAI配置...\")\n    try:\n        from config import OPENAI_CONFIG\n        import os\n        # 检查API Key\n        api_key = OPENAI_CONFIG[\"api_key\"]\n        if not api_key or api_key == \"your-openai-api-key\":\n            print(\"❌ 请在config.py中设置有效的OpenAI API Key\")", "detail": "test_config", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "test_config", "description": "test_config", "peekOfCode": "def main():\n    \"\"\"主测试函数\"\"\"\n    print(\"🧪 开始系统配置测试...\\n\")\n    tests = [\n        (\"Python包导入\", test_imports),\n        (\"配置文件\", test_config),\n        (\"Redis连接\", test_redis_connection),\n        (\"Qdrant连接\", test_qdrant_connection),\n        (\"OpenAI配置\", test_openai_config),\n    ]", "detail": "test_config", "documentation": {}}]